# 文档预览实施方案设计

## 1. 方案概述

基于技术调研和对比分析结果，本文档详细设计文档预览功能的具体实施方案，包括系统架构、技术实现、开发计划等。

## 2. 系统架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[用户浏览器] --> B[Nginx负载均衡]
    B --> C[FastAPI应用服务器]
    C --> D[文档处理服务]
    C --> E[Redis缓存]
    C --> F[文件存储系统]
    
    D --> G[LibreOffice转换服务]
    D --> H[PDF处理服务]
    D --> I[图片处理服务]
    
    E --> J[转换结果缓存]
    E --> K[预览URL缓存]
    
    F --> L[原始文档存储]
    F --> M[转换后文档存储]
```

### 2.2 核心组件设计

**文档预览服务架构：**

```python
# 服务层架构设计
class DocumentPreviewService:
    def __init__(self):
        self.converter = DocumentConverter()
        self.cache = DocumentCache()
        self.storage = FileStorage()
        
    async def get_preview(self, doc_id: str, doc_type: str) -> PreviewResponse:
        """统一预览入口"""
        # 1. 检查缓存
        cached_result = await self.cache.get_preview(doc_id)
        if cached_result:
            return cached_result
            
        # 2. 根据文档类型选择处理策略
        if doc_type in ['pdf']:
            return await self._handle_pdf_preview(doc_id)
        elif doc_type in ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt']:
            return await self._handle_office_preview(doc_id)
        elif doc_type in ['txt', 'md', 'json']:
            return await self._handle_text_preview(doc_id)
        elif doc_type in ['jpg', 'png', 'gif']:
            return await self._handle_image_preview(doc_id)
        else:
            raise UnsupportedDocumentType(f"不支持的文档类型: {doc_type}")
```

### 2.3 数据流设计

**文档预览流程：**

1. **用户请求** → 前端发起预览请求
2. **类型识别** → 后端识别文档类型
3. **缓存检查** → 检查是否有缓存的预览结果
4. **文档处理** → 根据类型选择处理策略
5. **结果缓存** → 缓存处理结果
6. **响应返回** → 返回预览内容或URL

## 3. 技术实现方案

### 3.1 LibreOffice集成实现

**Docker化部署：**

```dockerfile
# Dockerfile for LibreOffice Service
FROM ubuntu:20.04

RUN apt-get update && apt-get install -y \
    libreoffice \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip3 install -r requirements.txt

COPY converter_service.py .
CMD ["python3", "converter_service.py"]
```

**转换服务实现：**

```python
import asyncio
import subprocess
import tempfile
import os
from pathlib import Path

class LibreOfficeConverter:
    def __init__(self, max_concurrent=3):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.libreoffice_path = "/usr/bin/libreoffice"
        
    async def convert_to_pdf(self, input_file: str, output_dir: str) -> str:
        """异步转换Office文档为PDF"""
        async with self.semaphore:
            return await self._do_convert(input_file, output_dir)
            
    async def _do_convert(self, input_file: str, output_dir: str) -> str:
        """执行实际转换"""
        cmd = [
            self.libreoffice_path,
            '--headless',
            '--convert-to', 'pdf',
            '--outdir', output_dir,
            input_file
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            # 计算输出文件路径
            input_name = Path(input_file).stem
            output_file = os.path.join(output_dir, f"{input_name}.pdf")
            return output_file
        else:
            raise ConversionError(f"转换失败: {stderr.decode()}")
```

### 3.2 前端PDF.js集成

**Vue组件重构：**

```vue
<template>
  <div class="pdf-viewer-container">
    <!-- 工具栏 -->
    <div class="pdf-toolbar">
      <el-button-group>
        <el-button @click="previousPage" :disabled="currentPage <= 1">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-input-number 
          v-model="currentPage" 
          :min="1" 
          :max="totalPages"
          @change="goToPage"
          size="small"
        />
        <span class="page-info">/ {{ totalPages }}</span>
        <el-button @click="nextPage" :disabled="currentPage >= totalPages">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button @click="zoomOut" :disabled="scale <= 0.5">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
        <el-button @click="zoomIn" :disabled="scale >= 3">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
      </el-button-group>
    </div>
    
    <!-- PDF内容区域 -->
    <div class="pdf-content" ref="pdfContainer">
      <canvas ref="pdfCanvas"></canvas>
    </div>
  </div>
</template>

<script setup>
import * as pdfjsLib from 'pdfjs-dist';
import { ref, onMounted, watch } from 'vue';

// 配置PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdfjs-dist/pdf.worker.min.js';

const props = defineProps({
  url: String,
  initialScale: { type: Number, default: 1.0 }
});

const pdfCanvas = ref(null);
const pdfContainer = ref(null);
const pdf = ref(null);
const currentPage = ref(1);
const totalPages = ref(0);
const scale = ref(props.initialScale);

const loadPDF = async () => {
  try {
    pdf.value = await pdfjsLib.getDocument(props.url).promise;
    totalPages.value = pdf.value.numPages;
    await renderPage(currentPage.value);
  } catch (error) {
    console.error('PDF加载失败:', error);
  }
};

const renderPage = async (pageNum) => {
  if (!pdf.value) return;
  
  const page = await pdf.value.getPage(pageNum);
  const viewport = page.getViewport({ scale: scale.value });
  
  const canvas = pdfCanvas.value;
  const context = canvas.getContext('2d');
  canvas.height = viewport.height;
  canvas.width = viewport.width;
  
  await page.render({
    canvasContext: context,
    viewport: viewport
  }).promise;
};

// 页面控制方法
const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 缩放控制方法
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(scale.value + 0.25, 3);
  }
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(scale.value - 0.25, 0.5);
  }
};

const resetZoom = () => {
  scale.value = 1.0;
};

// 监听页面和缩放变化
watch(currentPage, (newPage) => {
  renderPage(newPage);
});

watch(scale, () => {
  renderPage(currentPage.value);
});

onMounted(() => {
  loadPDF();
});
</script>
```

### 3.3 缓存策略实现

**Redis缓存设计：**

```python
import redis
import json
import hashlib
from typing import Optional

class DocumentCache:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
        self.preview_ttl = 3600 * 24  # 24小时
        self.conversion_ttl = 3600 * 24 * 7  # 7天
        
    def _get_doc_hash(self, doc_id: str, doc_modified: str) -> str:
        """生成文档唯一标识"""
        return hashlib.md5(f"{doc_id}:{doc_modified}".encode()).hexdigest()
        
    async def get_preview_url(self, doc_id: str, doc_modified: str) -> Optional[str]:
        """获取预览URL缓存"""
        doc_hash = self._get_doc_hash(doc_id, doc_modified)
        cache_key = f"preview_url:{doc_hash}"
        
        cached_url = await self.redis.get(cache_key)
        if cached_url:
            return cached_url.decode()
        return None
        
    async def cache_preview_url(self, doc_id: str, doc_modified: str, preview_url: str):
        """缓存预览URL"""
        doc_hash = self._get_doc_hash(doc_id, doc_modified)
        cache_key = f"preview_url:{doc_hash}"
        
        await self.redis.setex(cache_key, self.preview_ttl, preview_url)
        
    async def get_conversion_result(self, doc_id: str, doc_modified: str) -> Optional[dict]:
        """获取转换结果缓存"""
        doc_hash = self._get_doc_hash(doc_id, doc_modified)
        cache_key = f"conversion:{doc_hash}"
        
        cached_result = await self.redis.get(cache_key)
        if cached_result:
            return json.loads(cached_result.decode())
        return None
        
    async def cache_conversion_result(self, doc_id: str, doc_modified: str, result: dict):
        """缓存转换结果"""
        doc_hash = self._get_doc_hash(doc_id, doc_modified)
        cache_key = f"conversion:{doc_hash}"
        
        await self.redis.setex(cache_key, self.conversion_ttl, json.dumps(result))
```

### 3.4 错误处理和监控

**异常处理框架：**

```python
class DocumentPreviewException(Exception):
    """文档预览基础异常"""
    pass

class UnsupportedDocumentType(DocumentPreviewException):
    """不支持的文档类型"""
    pass

class ConversionError(DocumentPreviewException):
    """文档转换错误"""
    pass

class PreviewGenerationError(DocumentPreviewException):
    """预览生成错误"""
    pass

# 异常处理装饰器
def handle_preview_errors(func):
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except UnsupportedDocumentType as e:
            logger.warning(f"不支持的文档类型: {e}")
            raise HTTPException(status_code=400, detail=str(e))
        except ConversionError as e:
            logger.error(f"文档转换失败: {e}")
            raise HTTPException(status_code=500, detail="文档转换失败")
        except PreviewGenerationError as e:
            logger.error(f"预览生成失败: {e}")
            raise HTTPException(status_code=500, detail="预览生成失败")
        except Exception as e:
            logger.error(f"未知错误: {e}")
            raise HTTPException(status_code=500, detail="服务器内部错误")
    return wrapper
```

## 4. 开发计划

### 4.1 开发阶段划分

**第一阶段：基础功能实现（3周）**

*Week 1: 后端核心服务*
- [ ] LibreOffice转换服务集成
- [ ] 文档类型识别和路由
- [ ] 基础API接口实现
- [ ] 错误处理框架

*Week 2: 前端组件重构*
- [ ] PDF.js集成和UI重构
- [ ] 前端直接解析集成（mammoth.js等）
- [ ] 预览组件统一接口
- [ ] 基础交互功能

*Week 3: 缓存和优化*
- [ ] Redis缓存实现
- [ ] 文件存储优化
- [ ] 性能监控集成
- [ ] 基础测试用例

**第二阶段：功能增强（2周）**

*Week 4: 高级功能*
- [ ] 转换进度跟踪
- [ ] 批量转换支持
- [ ] 智能转换策略
- [ ] 预览质量优化

*Week 5: 用户体验*
- [ ] 加载状态优化
- [ ] 错误提示改进
- [ ] 键盘快捷键支持
- [ ] 响应式设计优化

**第三阶段：生产就绪（1周）**

*Week 6: 部署和监控*
- [ ] Docker容器化
- [ ] 生产环境部署
- [ ] 监控和日志完善
- [ ] 性能调优

### 4.2 人力资源分配

**团队配置：**
- 后端开发工程师：1人（负责API和转换服务）
- 前端开发工程师：1人（负责Vue组件和交互）
- DevOps工程师：0.5人（负责部署和监控）

**工作量估算：**
- 总工作量：约120人天
- 开发周期：6周
- 并行开发，前后端同步进行

### 4.3 里程碑和交付物

**里程碑1（Week 3）：**
- 基础预览功能可用
- 支持PDF、Office、文本、图片预览
- 基础缓存机制

**里程碑2（Week 5）：**
- 完整功能实现
- 用户体验优化完成
- 性能达到预期指标

**里程碑3（Week 6）：**
- 生产环境部署完成
- 监控和日志系统就绪
- 文档和培训材料完成

## 5. 质量保证

### 5.1 测试策略

**单元测试：**
- 文档转换服务测试
- 缓存服务测试
- API接口测试
- 前端组件测试

**集成测试：**
- 端到端预览流程测试
- 不同文档类型测试
- 并发转换测试
- 缓存一致性测试

**性能测试：**
- 大文件转换性能测试
- 并发用户访问测试
- 内存和CPU使用率测试
- 缓存命中率测试

### 5.2 代码质量

**代码规范：**
- Python: PEP 8 + Black格式化
- JavaScript: ESLint + Prettier
- 代码审查机制
- 自动化质量检查

**文档要求：**
- API文档（OpenAPI/Swagger）
- 组件使用文档
- 部署运维文档
- 故障排查手册

## 6. 风险管理

### 6.1 技术风险

**风险1：LibreOffice转换性能不达预期**
- 概率：中等
- 影响：高
- 应对：准备OnlyOffice备选方案，实现转换服务可插拔

**风险2：大文件处理内存溢出**
- 概率：中等
- 影响：中等
- 应对：实现文件大小限制和分页处理

**风险3：并发转换资源竞争**
- 概率：低
- 影响：中等
- 应对：实现转换队列和资源池管理

### 6.2 进度风险

**风险1：前端组件集成复杂度超预期**
- 概率：中等
- 影响：中等
- 应对：提前进行技术验证，准备简化方案

**风险2：第三方依赖问题**
- 概率：低
- 影响：高
- 应对：选择稳定版本，准备备选方案

## 7. 总结

本实施方案基于深度技术调研，采用LibreOffice转PDF + PDF.js的核心架构，配合完善的缓存和错误处理机制。方案在技术可行性、实现复杂度、维护成本等方面达到最佳平衡，能够满足当前需求并为未来扩展提供良好基础。

建议按照三个阶段逐步实施，确保每个阶段都有可交付的成果，降低项目风险，保证质量和进度。
