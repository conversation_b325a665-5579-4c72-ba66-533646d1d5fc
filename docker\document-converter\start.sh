#!/bin/bash

# 文档转换服务启动脚本

set -e

echo "启动LibreOffice文档转换服务..."

# 检查LibreOffice是否可用
if ! command -v libreoffice &> /dev/null; then
    echo "错误: LibreOffice未安装"
    exit 1
fi

# 检查Python依赖
python3 -c "import fastapi, uvicorn, aioredis" || {
    echo "错误: Python依赖未正确安装"
    exit 1
}

# 创建必要的目录
mkdir -p /app/temp /app/output /app/logs

# 清理旧的临时文件
find /app/temp -type f -mtime +1 -delete 2>/dev/null || true

# 启动转换服务
echo "启动FastAPI服务器..."
exec python3 -m uvicorn converter_service:app \
    --host 0.0.0.0 \
    --port 8080 \
    --workers 1 \
    --log-level info \
    --access-log
