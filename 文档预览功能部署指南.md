# 文档预览功能部署指南

## 🎯 概述

本指南详细介绍了如何部署和使用增强的文档预览功能，包括LibreOffice转换服务、Redis缓存、前端PDF.js组件等完整解决方案。

## 📋 功能特性

### 核心功能
- ✅ **PDF预览**: 基于PDF.js的高质量PDF预览
- ✅ **Office转换**: LibreOffice自动转换Office文档为PDF
- ✅ **智能缓存**: Redis缓存机制提升性能
- ✅ **进度跟踪**: 实时转换进度监控
- ✅ **多格式支持**: PDF、Word、Excel、PowerPoint、图片、文本
- ✅ **响应式设计**: 适配桌面和移动设备

### 技术特性
- 🔄 **微服务架构**: 独立的转换服务
- ⚡ **高性能缓存**: 多层缓存策略
- 🛡️ **安全可靠**: 完善的错误处理和安全机制
- 📊 **监控告警**: Prometheus + Grafana监控
- 🐳 **容器化部署**: Docker + Docker Compose

## 🚀 快速开始

### 1. 环境要求

**系统要求:**
- Linux/macOS/Windows (推荐Ubuntu 20.04+)
- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB内存
- 至少10GB磁盘空间

**端口要求:**
- 80: Nginx反向代理
- 6379: Redis缓存
- 8080: 文档转换服务
- 9090: Prometheus监控 (可选)
- 3000: Grafana仪表板 (可选)

### 2. 一键部署

```bash
# 1. 克隆项目或下载部署文件
git clone <repository-url>
cd fastapi_best_architecture

# 2. 设置执行权限
chmod +x scripts/deploy-document-preview.sh

# 3. 启动服务
./scripts/deploy-document-preview.sh start

# 4. 检查服务状态
./scripts/deploy-document-preview.sh status
```

### 3. 验证部署

访问以下URL验证服务：

- **文档转换服务**: http://localhost:8080/health
- **Nginx代理**: http://localhost/nginx-health
- **主应用**: http://localhost

## 📁 项目结构

```
fastapi_best_architecture/
├── backend/app/iot/service/
│   ├── document_converter.py      # LibreOffice转换服务
│   ├── document_cache.py          # Redis缓存服务
│   └── document_service.py        # 文档预览服务(已重构)
├── src/components/FileManagement/
│   └── EnhancedDocumentPreview.vue # 增强的前端预览组件
├── docker/
│   ├── document-converter/        # 转换服务Docker配置
│   └── nginx/                     # Nginx配置
├── scripts/
│   └── deploy-document-preview.sh # 部署脚本
├── docker-compose.document-preview.yml # Docker Compose配置
└── 文档预览功能部署指南.md
```

## 🔧 配置说明

### 1. 后端配置

**document_converter.py** - LibreOffice转换服务
```python
# 主要配置项
LIBREOFFICE_PATH = "/usr/bin/libreoffice"  # LibreOffice路径
MAX_CONCURRENT = 3                         # 最大并发转换数
TIMEOUT = 300                             # 转换超时时间(秒)
SUPPORTED_FORMATS = {'.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'}
```

**document_cache.py** - Redis缓存配置
```python
# 缓存TTL设置
PREVIEW_URL_TTL = 3600 * 24      # 预览URL缓存24小时
CONVERSION_RESULT_TTL = 3600 * 24 * 7  # 转换结果缓存7天
DOCUMENT_INFO_TTL = 3600 * 12    # 文档信息缓存12小时
```

### 2. 前端配置

**EnhancedDocumentPreview.vue** - 前端预览组件
```javascript
// PDF.js配置
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdfjs-dist/pdf.worker.min.js';

// 支持的文档类型
const SUPPORTED_TYPES = {
  pdf: ['pdf'],
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  text: ['txt', 'md', 'json', 'xml', 'csv'],
  office: ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt']
};
```

### 3. Docker配置

**docker-compose.document-preview.yml** - 服务编排
```yaml
# 主要服务
services:
  redis-cache:        # Redis缓存
  document-converter: # LibreOffice转换服务
  nginx-proxy:        # Nginx反向代理
  preview-monitor:    # Prometheus监控 (可选)
  grafana:           # Grafana仪表板 (可选)
```

## 🛠️ 使用指南

### 1. API接口

**文档预览接口:**
```http
GET /api/iot/v1/documents/{kb_id}/{doc_id}/preview
```

**文档转换接口:**
```http
POST /api/iot/v1/documents/{kb_id}/{doc_id}/convert-to-pdf
```

**转换进度查询:**
```http
GET /api/iot/v1/documents/conversion-progress/{task_id}
```

**缓存管理:**
```http
DELETE /api/iot/v1/documents/{kb_id}/{doc_id}/cache  # 清除缓存
GET /api/iot/v1/documents/cache/stats               # 缓存统计
```

### 2. 前端组件使用

```vue
<template>
  <EnhancedDocumentPreview :document="documentData" />
</template>

<script setup>
import EnhancedDocumentPreview from '@/components/FileManagement/EnhancedDocumentPreview.vue';

const documentData = {
  id: 'doc123',
  kb_id: 'kb456',
  name: 'example.docx',
  type: 'docx',
  size: 1024000,
  create_time: '2024-01-01T00:00:00Z'
};
</script>
```

### 3. 管理命令

```bash
# 服务管理
./scripts/deploy-document-preview.sh start      # 启动服务
./scripts/deploy-document-preview.sh stop       # 停止服务
./scripts/deploy-document-preview.sh restart    # 重启服务
./scripts/deploy-document-preview.sh status     # 检查状态

# 日志查看
./scripts/deploy-document-preview.sh logs                    # 所有日志
./scripts/deploy-document-preview.sh logs document-converter # 转换服务日志
./scripts/deploy-document-preview.sh logs redis-cache       # Redis日志

# 监控和维护
./scripts/deploy-document-preview.sh monitoring  # 启动监控
./scripts/deploy-document-preview.sh build      # 重新构建镜像
./scripts/deploy-document-preview.sh cleanup    # 清理数据
```

## 📊 监控和维护

### 1. 服务监控

**Prometheus指标:**
- 转换任务数量和成功率
- 缓存命中率
- 响应时间统计
- 资源使用情况

**Grafana仪表板:**
- 实时性能监控
- 错误率趋势
- 资源使用图表
- 告警配置

### 2. 日志管理

**日志位置:**
```
logs/
├── nginx/          # Nginx访问和错误日志
├── converter/      # 转换服务日志
└── application/    # 应用日志
```

**日志轮转:**
```bash
# 配置logrotate
sudo vim /etc/logrotate.d/document-preview
```

### 3. 性能优化

**缓存优化:**
- 调整Redis内存限制
- 优化缓存TTL设置
- 监控缓存命中率

**转换优化:**
- 调整并发转换数量
- 优化LibreOffice配置
- 定期清理临时文件

## 🔒 安全配置

### 1. 网络安全

```nginx
# Nginx安全配置
add_header X-Frame-Options SAMEORIGIN;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
```

### 2. 文件安全

```python
# 文件类型验证
ALLOWED_EXTENSIONS = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
```

### 3. 访问控制

- JWT token验证
- 权限检查
- 文件访问限制

## 🐛 故障排除

### 1. 常见问题

**问题1: LibreOffice转换失败**
```bash
# 检查LibreOffice安装
docker exec document-converter libreoffice --version

# 查看转换日志
./scripts/deploy-document-preview.sh logs document-converter
```

**问题2: Redis连接失败**
```bash
# 检查Redis状态
docker exec document-preview-redis redis-cli ping

# 检查网络连接
docker network ls
```

**问题3: 前端预览异常**
```bash
# 检查PDF.js资源
curl -I http://localhost/pdfjs-dist/pdf.worker.min.js

# 检查API响应
curl -X GET "http://localhost/api/iot/v1/documents/{kb_id}/{doc_id}/preview"
```

### 2. 性能问题

**转换速度慢:**
- 增加转换服务实例
- 优化文档大小限制
- 检查系统资源使用

**缓存命中率低:**
- 调整缓存TTL设置
- 检查缓存键生成逻辑
- 监控缓存使用情况

## 📈 扩展和定制

### 1. 添加新的文档格式

```python
# 在document_converter.py中添加
SUPPORTED_FORMATS.add('.新格式')
```

### 2. 自定义前端组件

```vue
<!-- 扩展EnhancedDocumentPreview.vue -->
<template>
  <!-- 添加新的预览类型 -->
</template>
```

### 3. 集成其他转换服务

```python
# 创建新的转换器类
class CustomConverter:
    async def convert_to_pdf(self, input_file: str) -> str:
        # 自定义转换逻辑
        pass
```

## 📞 技术支持

如有问题或需要技术支持，请：

1. 查看日志文件获取详细错误信息
2. 检查服务状态和配置
3. 参考故障排除章节
4. 联系技术团队

---

**版本**: v1.0.0  
**更新时间**: 2025-01-13  
**维护者**: 文档预览项目组
