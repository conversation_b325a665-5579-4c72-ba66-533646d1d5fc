#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF生成功能
"""
import asyncio
import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.iot.service.document_service import document_service

def test_pdf_generation():
    try:
        print("🔧 测试PDF生成功能...")

        # 直接测试PDF生成方法
        pdf_bytes = document_service._generate_simple_pdf_content("测试文档.docx")

        print(f'PDF生成成功: {len(pdf_bytes)} 字节')
        print(f'PDF文件头: {pdf_bytes[:20]}')

        # 验证是否为有效PDF
        if pdf_bytes.startswith(b'%PDF'):
            print('✅ 有效的PDF格式')

            # 保存到文件进行验证
            with open('test_generated.pdf', 'wb') as f:
                f.write(pdf_bytes)
            print('✅ PDF文件已保存为 test_generated.pdf')

            # 显示PDF内容的一部分
            pdf_text = pdf_bytes.decode('utf-8', errors='ignore')
            print(f'PDF内容预览: {pdf_text[:200]}...')
        else:
            print('❌ 无效的PDF格式')
            print(f'实际文件头: {pdf_bytes[:50]}')

    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pdf_generation()
