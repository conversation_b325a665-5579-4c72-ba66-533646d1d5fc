<template>
  <div class="document-preview-container">
    <!-- 预览头部 -->
    <div class="preview-header">
      <div class="document-info">
        <div class="document-icon">
          <el-icon :size="24" :color="getFileIconColor(document.type || '')">
            <Document />
          </el-icon>
        </div>
        <div class="document-details">
          <h3 class="document-name">{{ document.name }}</h3>
          <div class="document-meta">
            <span>{{ formatFileSize(document.size || 0) }}</span>
            <span>•</span>
            <span>{{ getFileTypeLabel(document.type || '') }}</span>
            <span>•</span>
            <span>{{ formatDateTime(document.create_time || '') }}</span>
          </div>
        </div>
      </div>
      
      <div class="preview-actions">
        <el-button
          type="primary"
          :icon="Download"
          @click="downloadDocument"
          :loading="downloadLoading"
        >
          下载
        </el-button>
        <el-button
          :icon="FullScreen"
          @click="toggleFullscreen"
        >
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        <el-button
          :icon="Close"
          @click="closePreview"
        >
          关闭
        </el-button>
      </div>
    </div>
    
    <!-- 预览内容区域 -->
    <div 
      ref="previewContainer"
      class="preview-content"
      :class="{ 'fullscreen': isFullscreen }"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="preview-loading">
        <el-icon class="is-loading" :size="48">
          <Loading />
        </el-icon>
        <p>正在加载预览...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="preview-error">
        <el-icon :size="48" color="#F56C6C">
          <Warning />
        </el-icon>
        <p>{{ error }}</p>
        <el-button @click="retryPreview">重试</el-button>
      </div>
      
      <!-- 预览内容 -->
      <div v-else class="preview-viewer">
        <!-- PDF 预览 -->
        <div v-if="previewType === 'pdf'" class="pdf-viewer">
          <div class="pdf-toolbar">
            <div class="pdf-controls">
              <el-button-group>
                <el-button :icon="ZoomOut" @click="zoomOut" :disabled="scale <= 0.5" />
                <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
                <el-button :icon="ZoomIn" @click="zoomIn" :disabled="scale >= 3" />
              </el-button-group>
              
              <div class="page-controls">
                <el-button :icon="ArrowLeft" @click="prevPage" :disabled="currentPage <= 1" />
                <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
                <el-button :icon="ArrowRight" @click="nextPage" :disabled="currentPage >= totalPages" />
              </div>
            </div>
          </div>
          
          <div class="pdf-content" @wheel="handleWheel">
            <iframe
              v-if="previewUrl"
              :src="pdfViewerUrl"
              style="width: 100%; height: 100%; border: none;"
              @load="handlePdfLoad"
            />
          </div>
        </div>
        
        <!-- 图片预览 -->
        <div v-else-if="previewType === 'image'" class="image-viewer">
          <div class="image-toolbar">
            <el-button-group>
              <el-button :icon="ZoomOut" @click="zoomOut" :disabled="scale <= 0.1" />
              <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
              <el-button :icon="ZoomIn" @click="zoomIn" :disabled="scale >= 5" />
            </el-button-group>
            <el-button :icon="RefreshLeft" @click="rotateLeft" />
            <el-button :icon="RefreshRight" @click="rotateRight" />
          </div>
          
          <div class="image-content">
            <img
              :src="previewUrl"
              :style="imageStyle"
              @load="handleImageLoad"
              @error="handleImageError"
            />
          </div>
        </div>
        
        <!-- 文本预览 -->
        <div v-else-if="previewType === 'text'" class="text-viewer">
          <div class="text-toolbar">
            <el-button-group>
              <el-button @click="decreaseFontSize" :disabled="fontSize <= 12">A-</el-button>
              <el-button @click="resetFontSize">{{ fontSize }}px</el-button>
              <el-button @click="increaseFontSize" :disabled="fontSize >= 24">A+</el-button>
            </el-button-group>
            <el-switch
              v-model="wordWrap"
              active-text="自动换行"
              inactive-text="不换行"
            />
          </div>
          
          <div class="text-content">
            <pre
              :style="textStyle"
              v-html="highlightedText"
            />
          </div>
        </div>
        
        <!-- Office 文档预览 -->
        <div v-else-if="previewType === 'office'" class="office-viewer">
          <!-- PDF转换预览 -->
          <div v-if="convertedPdfUrl" class="converted-pdf-viewer">
            <iframe
              :src="convertedPdfViewerUrl"
              style="width: 100%; height: 100%; border: none;"
            />
          </div>

          <!-- Office文档信息显示 -->
          <div v-else class="office-placeholder">
            <div class="office-icon">
              <el-icon :size="64"><Document /></el-icon>
            </div>
            <h3>{{ props.document.name }}</h3>
            <p>{{ getOfficeTypeLabel(props.document.type) }} 文档</p>
            <div class="office-options">
              <el-button type="primary" @click="convertToPdf" :loading="convertLoading">
                转换为PDF预览
              </el-button>
              <el-button @click="downloadDocument">
                下载到本地查看
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported-viewer">
          <el-icon :size="64" color="#909399">
            <Document />
          </el-icon>
          <h3>暂不支持预览此文件类型</h3>
          <p>您可以下载文件后使用相应的应用程序打开</p>
          <el-button type="primary" @click="downloadDocument">
            下载文件
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 预览设置面板 -->
    <div v-if="showSettings" class="preview-settings">
      <el-card>
        <template #header>
          <span>预览设置</span>
        </template>
        
        <el-form label-width="80px" size="small">
          <el-form-item label="主题">
            <el-select v-model="theme" @change="applyTheme">
              <el-option label="浅色" value="light" />
              <el-option label="深色" value="dark" />
              <el-option label="护眼" value="sepia" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="previewType === 'text'" label="字体">
            <el-select v-model="fontFamily" @change="applyFont">
              <el-option label="系统默认" value="system" />
              <el-option label="等宽字体" value="monospace" />
              <el-option label="宋体" value="SimSun" />
              <el-option label="微软雅黑" value="Microsoft YaHei" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="previewType === 'pdf'" label="适应方式">
            <el-radio-group v-model="fitMode" @change="applyFitMode">
              <el-radio label="width">适应宽度</el-radio>
              <el-radio label="height">适应高度</el-radio>
              <el-radio label="page">适应页面</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Document,
  Download,
  FullScreen,
  Close,
  Loading,
  Warning,
  ZoomIn,
  ZoomOut,
  ArrowLeft,
  ArrowRight,
  RefreshLeft,
  RefreshRight
} from '@element-plus/icons-vue';

import { formatFileSize, type DocumentInfo } from '/@/api/iot/document';
import { Session } from '/@/utils/storage';
import { fastApiRequest } from '/@/api/iot/knowledgeBase';

// Props
interface Props {
  document: DocumentInfo;
  visible?: boolean;
  showSettings?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  showSettings: false
});

// Emits
const emit = defineEmits<{
  close: [];
  download: [document: DocumentInfo];
}>();

// 响应式数据
const loading = ref(false);
const error = ref('');
const downloadLoading = ref(false);
const previewUrl = ref('');
const previewContainer = ref<HTMLElement>();
const isFullscreen = ref(false);

// 预览控制
const scale = ref(1);
const rotation = ref(0);
const currentPage = ref(1);
const totalPages = ref(1);
const fontSize = ref(14);
const wordWrap = ref(true);

// 设置
const theme = ref('light');
const fontFamily = ref('system');
const fitMode = ref('width');

// 文本内容
const textContent = ref('');

// Office文档转换
const convertLoading = ref(false);
const convertedPdfUrl = ref('');
const requiresConversion = ref(false);
const conversionUrl = ref('');
const downloadUrl = ref('');

// 计算属性
const previewType = computed(() => {
  // 优先根据实际加载的内容判断
  if (textContent.value) return 'text';
  if (convertedPdfUrl.value) return 'pdf';
  if (previewUrl.value) {
    // 根据文档类型判断预览URL的类型
    const type = props.document.type?.toLowerCase() || '';
    if (type.includes('pdf')) return 'pdf';
    if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) return 'image';
    return 'pdf'; // 默认作为PDF处理
  }
  if (requiresConversion.value) return 'office';

  // 如果没有加载内容，根据文档类型推断
  const type = props.document.type?.toLowerCase() || '';
  if (type.includes('text') || ['txt', 'md', 'json', 'xml', 'csv'].includes(type)) return 'text';
  if (type.includes('pdf')) return 'pdf';
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) return 'image';
  if (type.includes('word') || type.includes('excel') || type.includes('powerpoint') ||
      ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(type)) return 'office';

  return 'unsupported';
});

const pdfViewerUrl = computed(() => {
  if (!previewUrl.value) return '';
  // 使用浏览器原生PDF预览，添加#toolbar=0隐藏工具栏
  return `${previewUrl.value}#toolbar=0&navpanes=0&scrollbar=0`;
});

const convertedPdfViewerUrl = computed(() => {
  if (!convertedPdfUrl.value) return '';
  // 使用浏览器原生PDF预览
  return `${convertedPdfUrl.value}#toolbar=0&navpanes=0&scrollbar=0`;
});

const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotation.value}deg)`,
  transition: 'transform 0.3s ease',
  maxWidth: '100%',
  maxHeight: '100%'
}));

const textStyle = computed(() => ({
  fontSize: `${fontSize.value}px`,
  fontFamily: getFontFamily(),
  whiteSpace: wordWrap.value ? 'pre-wrap' : 'pre',
  lineHeight: '1.6',
  padding: '20px',
  margin: 0,
  background: getThemeBackground(),
  color: getThemeColor()
}));

const highlightedText = computed(() => {
  // 这里可以添加语法高亮逻辑
  return textContent.value;
});

// 方法
const loadPreview = async () => {
  if (!props.document.id) return;

  // 重置状态
  loading.value = true;
  error.value = '';
  textContent.value = '';
  previewUrl.value = '';
  convertedPdfUrl.value = '';
  requiresConversion.value = false;
  conversionUrl.value = '';
  downloadUrl.value = '';
  error.value = '';

  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      error.value = '请先登录';
      console.warn('DocumentPreview: No token found, user needs to login');
      return;
    }

    // 调用新的预览API
    const response = await fastApiRequest.get(`/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/preview`);

    // 统一处理JSON响应
    const result = response.data;
    if (result.code === 200) {
      const data = result.data;
      console.log('预览数据:', data);

      // 根据content_type处理不同类型的文档
      switch (data.content_type) {
        case 'text/plain':
          if (data.content) {
            // 直接显示文本内容
            textContent.value = data.content;
            console.log('显示文本内容，长度:', data.content.length);
          } else if (data.url) {
            // 通过URL获取文本内容
            console.log('通过URL获取文本内容:', data.url);
            await loadTextContentFromUrl(data.url);
          } else {
            throw new Error('文本文档缺少内容或URL');
          }
          break;

        case 'application/pdf':
        case 'pdf':
          if (data.url) {
            previewUrl.value = data.url;
            console.log('设置PDF预览URL:', data.url);
          } else {
            throw new Error('PDF文档缺少预览URL');
          }
          break;

        case 'image':
          if (data.url) {
            previewUrl.value = data.url;
            console.log('设置图片预览URL:', data.url);
          } else {
            throw new Error('图片文档缺少预览URL');
          }
          break;

        case 'office':
          if (data.requires_conversion) {
            // Office文档需要转换
            requiresConversion.value = true;
            conversionUrl.value = data.conversion_url || '';
            downloadUrl.value = data.download_url || '';
            console.log('Office文档需要转换:', {
              conversionUrl: conversionUrl.value,
              downloadUrl: downloadUrl.value
            });
            // 不显示提示消息，直接显示转换选项
          } else if (data.url) {
            // Office文档已转换，按PDF处理
            console.log('Office文档已转换为PDF，直接预览:', data.url);
            previewUrl.value = data.url;
            // 设置为PDF类型，让组件自动处理PDF预览
            console.log('已转换Office文档，设置为PDF预览模式');
          } else {
            throw new Error('Office文档缺少预览信息');
          }
          break;

        default:
          // 未知类型或不支持的类型
          if (data.url) {
            console.log('未知类型，尝试作为下载链接:', data.url);
            ElMessage.warning(data.message || '该文档格式不支持在线预览，请下载查看');
          } else {
            throw new Error(data.message || '不支持的文档类型');
          }
          break;
      }
    } else {
      throw new Error(result.message || '预览失败');
    }
  } catch (err) {
    error.value = '加载预览失败';
    console.error('Preview load error:', err);
    // 只有在不是认证错误时才显示错误消息
    if (err instanceof Error && !err.message.includes('请先登录')) {
      ElMessage.error(`加载预览失败: ${err.message}`);
    }
  } finally {
    loading.value = false;
  }
};

const loadTextContentFromUrl = async (url: string) => {
  try {
    console.log('从URL加载文本内容:', url);

    // 获取token
    const token = Session.get('token');
    if (!token) {
      throw new Error('请先登录');
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;

    const response = await fetch(fullUrl, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/plain, application/json, */*'
      }
    });

    if (response.ok) {
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        // 如果返回的是JSON，可能是错误响应
        const jsonData = await response.json();
        if (jsonData.code === 200 && jsonData.data?.content) {
          textContent.value = jsonData.data.content;
        } else {
          throw new Error(jsonData.message || '获取文本内容失败');
        }
      } else {
        // 直接作为文本处理
        const text = await response.text();
        textContent.value = text;
        console.log('成功加载文本内容，长度:', text.length);
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (err) {
    console.error('从URL加载文本内容失败:', err);
    error.value = `加载文本内容失败: ${err instanceof Error ? err.message : '未知错误'}`;
    ElMessage.error(`加载文本内容失败: ${err instanceof Error ? err.message : '未知错误'}`);
  }
};



const retryPreview = () => {
  loadPreview();
};

const downloadDocument = async () => {
  downloadLoading.value = true;
  try {
    emit('download', props.document);

    // 获取token
    const token = Session.get('token');
    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    // 使用fastApiRequest下载文件
    const response = await fastApiRequest.get(`/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`, {
      responseType: 'blob'
    });

    const blob = response.data;
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = props.document.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('开始下载文件');
  } catch (err) {
    console.error('下载失败:', err);
    ElMessage.error(`下载失败: ${err instanceof Error ? err.message : '未知错误'}`);
  } finally {
    downloadLoading.value = false;
  }
};

const toggleFullscreen = () => {
  if (!previewContainer.value) return;
  
  if (!isFullscreen.value) {
    if (previewContainer.value.requestFullscreen) {
      previewContainer.value.requestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

const closePreview = () => {
  emit('close');
};

// 缩放控制
const zoomIn = () => {
  if (previewType.value === 'image') {
    scale.value = Math.min(scale.value * 1.2, 5);
  } else {
    scale.value = Math.min(scale.value * 1.1, 3);
  }
};

const zoomOut = () => {
  if (previewType.value === 'image') {
    scale.value = Math.max(scale.value / 1.2, 0.1);
  } else {
    scale.value = Math.max(scale.value / 1.1, 0.5);
  }
};

const resetZoom = () => {
  scale.value = 1;
};

const handleWheel = (e: WheelEvent) => {
  if (e.ctrlKey) {
    e.preventDefault();
    if (e.deltaY < 0) {
      zoomIn();
    } else {
      zoomOut();
    }
  }
};

// 旋转控制
const rotateLeft = () => {
  rotation.value -= 90;
};

const rotateRight = () => {
  rotation.value += 90;
};

// 页面控制
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 字体控制
const increaseFontSize = () => {
  fontSize.value = Math.min(fontSize.value + 2, 24);
};

const decreaseFontSize = () => {
  fontSize.value = Math.max(fontSize.value - 2, 12);
};

const resetFontSize = () => {
  fontSize.value = 14;
};

// 主题和样式
const getFontFamily = () => {
  const fontMap: Record<string, string> = {
    system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    monospace: 'Monaco, Consolas, "Courier New", monospace',
    SimSun: 'SimSun, serif',
    'Microsoft YaHei': '"Microsoft YaHei", sans-serif'
  };
  return fontMap[fontFamily.value] || fontMap.system;
};

const getThemeBackground = () => {
  const bgMap: Record<string, string> = {
    light: '#ffffff',
    dark: '#1e1e1e',
    sepia: '#f4f1ea'
  };
  return bgMap[theme.value] || bgMap.light;
};

const getThemeColor = () => {
  const colorMap: Record<string, string> = {
    light: '#303133',
    dark: '#e4e7ed',
    sepia: '#5c4b37'
  };
  return colorMap[theme.value] || colorMap.light;
};

const applyTheme = () => {
  // 应用主题样式
};

const applyFont = () => {
  // 应用字体样式
};

const applyFitMode = () => {
  // 应用适应模式
};

// 事件处理
const handlePdfLoad = () => {
  // PDF 加载完成
};

const handleImageLoad = () => {
  // 图片加载完成
};

const handleImageError = () => {
  error.value = '图片加载失败';
};

const convertToPdf = async () => {
  if (!props.document.id || !props.document.kb_id) {
    ElMessage.error('缺少文档信息');
    return;
  }

  convertLoading.value = true;
  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    // 使用存储的转换URL，如果没有则使用默认URL
    const apiUrl = conversionUrl.value || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/convert-to-pdf`;
    console.log('调用转换API:', apiUrl);

    // 调用后端API将Office文档转换为PDF
    const response = await fastApiRequest.post(apiUrl);

    const result = response.data;
    if (result.code === 200) {
      const data = result.data;

      if (data.status === 'text_preview') {
        // 文本文件，直接加载预览
        ElMessage.success('文本文件可直接预览');
        loadPreview(); // 重新加载预览
      } else if (data.status === 'pdf_ready') {
        // PDF文件，直接预览
        ElMessage.success('PDF文件可直接预览');
        loadPreview(); // 重新加载预览
      } else if (data.status === 'not_supported') {
        // 不支持转换的文件类型
        ElMessage.info(data.message || '此文档类型不支持转换');
      } else if (data.pdf_url) {
        // Office文档转换成功
        convertedPdfUrl.value = data.pdf_url;

        // 如果有任务ID，监控转换进度
        if (data.task_id) {
          monitorConversionProgress(data.task_id);
        }

        ElMessage.success('转换成功');
      } else {
        throw new Error(data.message || '转换失败');
      }
    } else {
      throw new Error(result.message || '转换失败');
    }
  } catch (err) {
    console.error('PDF转换失败:', err);
    ElMessage.error(`PDF转换失败: ${err instanceof Error ? err.message : '未知错误'}`);
  } finally {
    convertLoading.value = false;
  }
};

// 监控转换进度
const monitorConversionProgress = async (taskId: string) => {

  const checkProgress = async () => {
    try {
      const response = await fastApiRequest.get(`/api/iot/v1/documents/conversion-progress/${taskId}`);

      const result = response.data;
      if (result.code === 200) {
        const progress = result.data;

        // 显示进度信息
        if (progress.status === 'completed') {
          ElMessage.success('文档转换完成');
          // 重新加载预览
          loadPreview();
        } else if (progress.status === 'failed') {
          ElMessage.error('文档转换失败');
        } else if (progress.status === 'processing') {
          ElMessage.info(`转换进度: ${Math.round(progress.progress * 100)}%`);
          // 继续监控
          setTimeout(checkProgress, 2000);
        }
      }
    } catch (error) {
      console.error('获取转换进度失败:', error);
    }
  };

  checkProgress();
};

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 工具函数
const getFileTypeLabel = (mimeType: string) => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/msword': 'Word',
    'text/plain': 'TXT',
    'text/markdown': 'Markdown'
  };
  return typeMap[mimeType] || '文档';
};

const getOfficeTypeLabel = (fileType?: string) => {
  const type = fileType?.toLowerCase() || '';
  if (type.includes('word') || type.includes('doc')) return 'Word';
  if (type.includes('excel') || type.includes('xls')) return 'Excel';
  if (type.includes('powerpoint') || type.includes('ppt')) return 'PowerPoint';
  return 'Office';
};

const getFileIconColor = (mimeType: string) => {
  if (mimeType?.includes('pdf')) return '#F56C6C';
  if (mimeType?.includes('word')) return '#409EFF';
  if (mimeType?.includes('sheet') || mimeType?.includes('excel')) return '#67C23A';
  if (mimeType?.includes('text')) return '#909399';
  return '#409EFF';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  // 检查是否有token再加载预览
  const token = Session.get('token');
  if (token) {
    loadPreview();
  } else {
    console.log('DocumentPreview: No token found, waiting for user login');
  }
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});

// 监听文档变化
watch(() => props.document, () => {
  if (props.document.id) {
    const token = Session.get('token');
    if (token) {
      loadPreview();
    }
  }
}, { deep: true });

// 监听token变化（当用户登录后自动加载预览）
watch(() => Session.get('token'), (newToken) => {
  if (newToken && props.document.id && !previewUrl.value && !textContent.value) {
    loadPreview();
  }
});

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && props.document.id) {
    loadPreview();
  }
});
</script>

<style scoped>
.document-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-details {
  flex: 1;
}

.document-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.document-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-content.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #fff;
}

.preview-loading,
.preview-error,
.unsupported-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #909399;
}

.preview-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pdf-viewer,
.image-viewer,
.text-viewer,
.office-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.office-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.office-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-weight: 500;
}



.office-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #909399;
}

.office-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.office-placeholder h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.office-placeholder p {
  margin: 0 0 24px 0;
  color: #909399;
  font-size: 14px;
}

.office-options {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
}

.converted-pdf-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pdf-toolbar,
.image-toolbar,
.text-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
}

.pdf-controls,
.page-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  min-width: 60px;
  text-align: center;
}

.pdf-content,
.image-content,
.text-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.image-content {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
}

.text-content {
  background: #fff;
}

.preview-settings {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 250px;
  z-index: 100;
}

@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .document-info {
    justify-content: center;
  }
  
  .preview-actions {
    justify-content: center;
  }
  
  .pdf-toolbar,
  .image-toolbar,
  .text-toolbar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
