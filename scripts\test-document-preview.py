#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档预览功能测试脚本

测试文档预览API的各项功能
"""
import requests
import json
import time
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/iot/v1/documents"

# 测试用的知识库ID和文档ID（需要根据实际情况修改）
TEST_KB_ID = "87a3346677ee111f0a8f37e63526e5b16"
TEST_DOC_ID = "test_document_id"  # 需要替换为实际的文档ID

# 测试用的JWT Token
TEST_TOKEN = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE0MWNiMWY2LTAyODAtNDFhYy1hY2FjLTc1NmE2N2I3MTcxNCJ9.5NfjF9W4UzTlj9w_crjcNnd_-gc552QbfOgRnL3lMzMGYnXPpmNQy3s2Qm9eCS5xOHwhR3hpZbdeh-zMSbQBDg"

def get_headers():
    """获取请求头"""
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    if TEST_TOKEN:
        headers["Authorization"] = f"Bearer {TEST_TOKEN}"
    return headers

def test_api_endpoint(method: str, url: str, data: Dict[Any, Any] = None) -> Dict[Any, Any]:
    """测试API端点"""
    print(f"\n🔍 测试 {method} {url}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=get_headers(), timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, headers=get_headers(), json=data, timeout=30)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=get_headers(), timeout=30)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return {}
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功")
            print(f"📄 响应: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")
            return result
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return {}
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时")
        return {}
    except requests.exceptions.ConnectionError:
        print(f"🔌 连接失败")
        return {}
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return {}

def test_document_list():
    """测试文档列表API"""
    print("\n" + "="*60)
    print("📋 测试文档列表API")
    print("="*60)
    
    url = f"{API_BASE}/{TEST_KB_ID}/list/page=1&page_size=10"
    result = test_api_endpoint("GET", url)
    
    if result and result.get("code") == 200:
        documents = result.get("data", {}).get("items", [])
        print(f"📊 找到 {len(documents)} 个文档")
        
        if documents:
            # 使用第一个文档进行后续测试
            global TEST_DOC_ID
            TEST_DOC_ID = documents[0].get("id", "")
            print(f"🎯 将使用文档ID进行测试: {TEST_DOC_ID}")
            return documents[0]
    
    return None

def test_document_preview(doc_id: str):
    """测试文档预览API"""
    print("\n" + "="*60)
    print("👁️ 测试文档预览API")
    print("="*60)
    
    url = f"{API_BASE}/{TEST_KB_ID}/{doc_id}/preview"
    result = test_api_endpoint("GET", url)
    
    if result and result.get("code") == 200:
        data = result.get("data", {})
        content_type = data.get("content_type", "")
        
        print(f"📄 内容类型: {content_type}")
        
        if content_type == "text/plain":
            content = data.get("content", "")
            print(f"📝 文本内容长度: {len(content)} 字符")
            print(f"📝 内容预览: {content[:200]}...")
        elif data.get("url"):
            print(f"🔗 预览URL: {data.get('url')}")
        elif data.get("requires_conversion"):
            print(f"🔄 需要转换: {data.get('doc_type')}")
            return test_document_conversion(doc_id)
    
    return result

def test_document_conversion(doc_id: str):
    """测试文档转换API"""
    print("\n" + "="*60)
    print("🔄 测试文档转换API")
    print("="*60)
    
    url = f"{API_BASE}/{TEST_KB_ID}/{doc_id}/convert-to-pdf"
    result = test_api_endpoint("POST", url)
    
    if result and result.get("code") == 200:
        data = result.get("data", {})
        task_id = data.get("task_id")
        
        if task_id:
            print(f"📋 转换任务ID: {task_id}")
            return test_conversion_progress(task_id)
        elif data.get("pdf_url"):
            print(f"📄 PDF URL: {data.get('pdf_url')}")
    
    return result

def test_conversion_progress(task_id: str):
    """测试转换进度API"""
    print("\n" + "="*60)
    print("📊 测试转换进度API")
    print("="*60)
    
    url = f"{API_BASE}/conversion-progress/{task_id}"
    
    for i in range(5):  # 最多检查5次
        print(f"\n🔍 第 {i+1} 次检查进度...")
        result = test_api_endpoint("GET", url)
        
        if result and result.get("code") == 200:
            data = result.get("data", {})
            progress = data.get("progress", 0)
            status = data.get("status", "")
            message = data.get("message", "")
            
            print(f"📊 进度: {progress*100:.1f}%")
            print(f"📋 状态: {status}")
            print(f"💬 消息: {message}")
            
            if status in ["completed", "failed"]:
                break
        
        time.sleep(2)  # 等待2秒后再次检查
    
    return result

def test_cache_management():
    """测试缓存管理API"""
    print("\n" + "="*60)
    print("🗄️ 测试缓存管理API")
    print("="*60)
    
    # 测试缓存统计
    print("\n📊 测试缓存统计...")
    url = f"{API_BASE}/cache/stats"
    stats_result = test_api_endpoint("GET", url)
    
    # 测试清除缓存
    if TEST_DOC_ID:
        print(f"\n🗑️ 测试清除文档缓存...")
        url = f"{API_BASE}/{TEST_KB_ID}/{TEST_DOC_ID}/cache"
        clear_result = test_api_endpoint("DELETE", url)
    
    return stats_result

def test_health_check():
    """测试健康检查"""
    print("\n" + "="*60)
    print("❤️ 测试服务健康状态")
    print("="*60)

    # 测试主应用健康状态 - 使用docs端点作为健康检查
    url = f"{BASE_URL}/docs"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ 服务正常运行: {url}")
            health_result = {"code": 200, "message": "Service is running"}
        else:
            print(f"❌ 服务状态异常: {response.status_code}")
            health_result = {"code": response.status_code, "message": "Service error"}
    except Exception as e:
        print(f"❌ 服务连接失败: {e}")
        health_result = {"code": 500, "message": str(e)}
    
    # 测试API文档访问
    url = f"{BASE_URL}/docs"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ API文档可访问: {url}")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")
    
    return health_result

def main():
    """主测试函数"""
    print("🚀 文档预览功能测试开始")
    print("="*80)
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ 服务未运行，请先启动FastAPI应用")
            return
    except:
        print("❌ 无法连接到服务，请确保FastAPI应用在8000端口运行")
        return
    
    print("✅ 服务连接正常")
    
    # 提示设置Token
    if not TEST_TOKEN:
        print("\n⚠️ 警告: 未设置TEST_TOKEN，某些需要认证的API可能失败")
        print("💡 请在浏览器中登录后，从开发者工具获取JWT token并设置到脚本中")
    
    # 执行测试
    test_results = {}
    
    # 1. 健康检查
    test_results["health"] = test_health_check()
    
    # 2. 文档列表
    doc_info = test_document_list()
    
    # 3. 文档预览（如果有文档）
    if TEST_DOC_ID and TEST_DOC_ID != "test_document_id":
        test_results["preview"] = test_document_preview(TEST_DOC_ID)
    else:
        print("\n⚠️ 跳过文档预览测试 - 没有可用的文档ID")
    
    # 4. 缓存管理
    test_results["cache"] = test_cache_management()
    
    # 测试总结
    print("\n" + "="*80)
    print("📋 测试总结")
    print("="*80)
    
    success_count = sum(1 for result in test_results.values() if result and result.get("code") == 200)
    total_count = len(test_results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有测试通过！文档预览功能正常")
    else:
        print("⚠️ 部分测试失败，请检查日志")
    
    print(f"\n🌐 访问地址:")
    print(f"   主应用: {BASE_URL}")
    print(f"   API文档: {BASE_URL}/docs")
    print(f"   文档预览API: {API_BASE}")

if __name__ == "__main__":
    main()
