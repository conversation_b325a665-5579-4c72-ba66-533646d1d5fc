#!/bin/bash

# 文档预览功能启动脚本

echo "启动文档预览功能..."

# 检查Redis服务
if ! redis-cli ping > /dev/null 2>&1; then
    echo "启动Redis服务..."
    # 根据系统选择启动方式
    if command -v systemctl > /dev/null; then
        sudo systemctl start redis
    elif command -v service > /dev/null; then
        sudo service redis-server start
    elif command -v docker > /dev/null; then
        echo "使用Docker启动Redis..."
        docker run -d --name redis-preview -p 6379:6379 redis:alpine
    else
        echo "请手动启动Redis服务"
        exit 1
    fi
fi

# 启动FastAPI应用
echo "启动FastAPI应用..."
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

echo "文档预览功能启动完成！"
echo "访问: http://localhost:8000"
