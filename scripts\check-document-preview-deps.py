#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档预览功能依赖检查脚本

检查系统是否具备启动文档预览功能的所有依赖
"""
import sys
import os
import subprocess
import importlib
from pathlib import Path

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def log_info(msg):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {msg}")

def log_success(msg):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {msg}")

def log_warning(msg):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {msg}")

def log_error(msg):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {msg}")

def check_python_version():
    """检查Python版本"""
    log_info("检查Python版本...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        log_success(f"Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        log_error(f"Python版本过低: {version.major}.{version.minor}.{version.micro}，需要Python 3.8+")
        return False

def check_python_packages():
    """检查Python包依赖"""
    log_info("检查Python包依赖...")
    
    required_packages = [
        'redis',
        'aioredis', 
        'fastapi',
        'uvicorn',
        'aiofiles',
        'asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'aioredis':
                # aioredis可能没有直接安装，但redis包含了异步支持
                import redis.asyncio
                log_success(f"✓ {package} (通过redis.asyncio)")
            else:
                importlib.import_module(package)
                log_success(f"✓ {package}")
        except ImportError:
            log_error(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        log_error(f"缺少以下Python包: {', '.join(missing_packages)}")
        log_info("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_redis_connection():
    """检查Redis连接"""
    log_info("检查Redis连接...")
    
    try:
        import redis
        
        # 尝试连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        r.ping()
        log_success("Redis连接正常")
        return True
    except redis.ConnectionError:
        log_warning("Redis连接失败 - Redis服务可能未启动")
        log_info("请启动Redis服务或使用Docker: docker run -d -p 6379:6379 redis:alpine")
        return False
    except Exception as e:
        log_error(f"Redis连接检查失败: {e}")
        return False

def check_libreoffice():
    """检查LibreOffice安装"""
    log_info("检查LibreOffice安装...")
    
    try:
        # 检查libreoffice命令
        result = subprocess.run(['libreoffice', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            log_success(f"LibreOffice已安装: {version}")
            return True
        else:
            log_error("LibreOffice命令执行失败")
            return False
    except FileNotFoundError:
        log_warning("LibreOffice未安装")
        log_info("请安装LibreOffice:")
        log_info("  Ubuntu/Debian: sudo apt-get install libreoffice")
        log_info("  CentOS/RHEL: sudo yum install libreoffice")
        log_info("  macOS: brew install --cask libreoffice")
        log_info("  Windows: 从官网下载安装 https://www.libreoffice.org/")
        return False
    except subprocess.TimeoutExpired:
        log_error("LibreOffice版本检查超时")
        return False
    except Exception as e:
        log_error(f"LibreOffice检查失败: {e}")
        return False

def check_file_structure():
    """检查文件结构"""
    log_info("检查文件结构...")
    
    required_files = [
        'backend/app/iot/service/document_converter.py',
        'backend/app/iot/service/document_cache.py',
        'backend/app/iot/service/document_service.py',
        'backend/app/iot/api/v1/document.py',
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            log_success(f"✓ {file_path}")
        else:
            log_error(f"✗ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        log_error(f"缺少以下文件: {', '.join(missing_files)}")
        return False
    
    return True

def check_env_config():
    """检查环境配置"""
    log_info("检查环境配置...")
    
    # 检查.env文件
    env_file = Path('.env')
    if not env_file.exists():
        log_warning(".env文件不存在")
        return False
    
    # 检查Redis配置
    with open(env_file, 'r', encoding='utf-8') as f:
        env_content = f.read()
    
    required_configs = [
        'REDIS_HOST',
        'REDIS_PORT', 
        'REDIS_PASSWORD',
        'REDIS_DATABASE'
    ]
    
    missing_configs = []
    for config in required_configs:
        if config not in env_content:
            missing_configs.append(config)
        else:
            log_success(f"✓ {config}")
    
    if missing_configs:
        log_warning(f"缺少以下环境配置: {', '.join(missing_configs)}")
        log_info("请在.env文件中添加Redis配置")
        return False
    
    return True

def check_docker_availability():
    """检查Docker可用性（可选）"""
    log_info("检查Docker可用性（可选）...")
    
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            log_success(f"Docker可用: {result.stdout.strip()}")
            
            # 检查docker-compose
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                log_success(f"Docker Compose可用: {result.stdout.strip()}")
                return True
            else:
                log_warning("Docker Compose不可用")
                return False
        else:
            log_warning("Docker不可用")
            return False
    except FileNotFoundError:
        log_warning("Docker未安装")
        return False
    except Exception as e:
        log_warning(f"Docker检查失败: {e}")
        return False

def generate_startup_script():
    """生成启动脚本"""
    log_info("生成启动脚本...")
    
    startup_script = """#!/bin/bash

# 文档预览功能启动脚本

echo "启动文档预览功能..."

# 检查Redis服务
if ! redis-cli ping > /dev/null 2>&1; then
    echo "启动Redis服务..."
    # 根据系统选择启动方式
    if command -v systemctl > /dev/null; then
        sudo systemctl start redis
    elif command -v service > /dev/null; then
        sudo service redis-server start
    elif command -v docker > /dev/null; then
        echo "使用Docker启动Redis..."
        docker run -d --name redis-preview -p 6379:6379 redis:alpine
    else
        echo "请手动启动Redis服务"
        exit 1
    fi
fi

# 启动FastAPI应用
echo "启动FastAPI应用..."
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

echo "文档预览功能启动完成！"
echo "访问: http://localhost:8000"
"""
    
    with open('scripts/start-document-preview.sh', 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    os.chmod('scripts/start-document-preview.sh', 0o755)
    log_success("启动脚本已生成: scripts/start-document-preview.sh")

def main():
    """主函数"""
    print("=" * 60)
    print("文档预览功能依赖检查")
    print("=" * 60)
    
    checks = [
        ("Python版本", check_python_version),
        ("Python包依赖", check_python_packages),
        ("Redis连接", check_redis_connection),
        ("LibreOffice安装", check_libreoffice),
        ("文件结构", check_file_structure),
        ("环境配置", check_env_config),
        ("Docker可用性", check_docker_availability),
    ]
    
    results = []
    
    for name, check_func in checks:
        print(f"\n{'-' * 40}")
        result = check_func()
        results.append((name, result))
    
    print(f"\n{'=' * 60}")
    print("检查结果汇总:")
    print("=" * 60)
    
    all_passed = True
    critical_failed = False
    
    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        color = Colors.GREEN if result else Colors.RED
        print(f"{color}{status}{Colors.NC} {name}")
        
        if not result:
            all_passed = False
            # 关键依赖检查
            if name in ["Python版本", "Python包依赖", "文件结构"]:
                critical_failed = True
    
    print("=" * 60)
    
    if critical_failed:
        log_error("关键依赖检查失败，无法启动文档预览功能")
        log_info("请解决上述问题后重新检查")
        return False
    elif all_passed:
        log_success("所有检查通过！可以启动文档预览功能")
        generate_startup_script()
        log_info("运行以下命令启动服务:")
        log_info("  ./scripts/start-document-preview.sh")
        return True
    else:
        log_warning("部分检查失败，但不影响基本功能")
        log_info("建议解决警告问题以获得最佳体验")
        generate_startup_script()
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
