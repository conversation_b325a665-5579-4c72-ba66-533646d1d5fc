<!DOCTYPE html>
<html>
<head>
    <title>Token Debug</title>
</head>
<body>
    <h1>Token Debug</h1>
    <div id="results"></div>
    
    <script>
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }
        
        const token = getCookie('token');
        const results = document.getElementById('results');
        
        results.innerHTML = `
            <p><strong>Token from Cookie:</strong> ${token || 'Not found'}</p>
            <p><strong>All Cookies:</strong> ${document.cookie}</p>
            <p><strong>LocalStorage token:</strong> ${localStorage.getItem('token') || 'Not found'}</p>
            <p><strong>SessionStorage token:</strong> ${sessionStorage.getItem('token') || 'Not found'}</p>
        `;
        
        // 测试API调用
        if (token) {
            fetch('/fastapi/api/iot/v1/documents/conversion-progress/test-task-123', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                results.innerHTML += `<p><strong>API Test Result:</strong> ${JSON.stringify(data)}</p>`;
            })
            .catch(error => {
                results.innerHTML += `<p><strong>API Test Error:</strong> ${error.message}</p>`;
            });
        } else {
            results.innerHTML += `<p><strong>API Test:</strong> No token available</p>`;
        }
    </script>
</body>
</html>
