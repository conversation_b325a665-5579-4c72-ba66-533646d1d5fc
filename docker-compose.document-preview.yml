version: '3.8'

services:
  # Redis缓存服务
  redis-cache:
    image: redis:7-alpine
    container_name: document-preview-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - document-preview

  # LibreOffice文档转换服务
  document-converter:
    build:
      context: ./docker/document-converter
      dockerfile: Dockerfile
    container_name: document-converter
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - converter_temp:/app/temp
      - converter_output:/app/output
      - converter_logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
    depends_on:
      redis-cache:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - document-preview
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Nginx反向代理
  nginx-proxy:
    image: nginx:alpine
    container_name: document-preview-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
      - static_files:/var/www/static:ro
    depends_on:
      - document-converter
    networks:
      - document-preview
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

  # 文档预览服务监控
  preview-monitor:
    image: prom/prometheus:latest
    container_name: document-preview-monitor
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - document-preview
    profiles:
      - monitoring

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: document-preview-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - preview-monitor
    networks:
      - document-preview
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  converter_temp:
    driver: local
  converter_output:
    driver: local
  converter_logs:
    driver: local
  nginx_logs:
    driver: local
  static_files:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  document-preview:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
