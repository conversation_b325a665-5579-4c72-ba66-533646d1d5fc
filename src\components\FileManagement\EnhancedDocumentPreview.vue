<template>
  <div class="enhanced-document-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
      <div class="loading-text">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>{{ loadingMessage }}</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-result
        icon="error"
        :title="error"
        :sub-title="errorDetails"
      >
        <template #extra>
          <el-button type="primary" @click="retryPreview">重试</el-button>
          <el-button @click="downloadDocument">下载文档</el-button>
        </template>
      </el-result>
    </div>

    <!-- 预览内容 -->
    <div v-else class="preview-container">
      <!-- 工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <el-button-group v-if="previewType === 'pdf'">
            <el-button @click="previousPage" :disabled="currentPage <= 1" size="small">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-input-number 
              v-model="currentPage" 
              :min="1" 
              :max="totalPages"
              @change="goToPage"
              size="small"
              style="width: 80px;"
            />
            <span class="page-info">/ {{ totalPages }}</span>
            <el-button @click="nextPage" :disabled="currentPage >= totalPages" size="small">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </el-button-group>
          
          <el-button-group>
            <el-button @click="zoomOut" :disabled="scale <= 0.5" size="small">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom" size="small">{{ Math.round(scale * 100) }}%</el-button>
            <el-button @click="zoomIn" :disabled="scale >= 3" size="small">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <el-button @click="toggleFullscreen" size="small">
            <el-icon><FullScreen /></el-icon>
          </el-button>
          <el-button @click="downloadDocument" size="small">
            <el-icon><Download /></el-icon>
          </el-button>
          <el-dropdown @command="handleMenuCommand">
            <el-button size="small">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="refresh">刷新预览</el-dropdown-item>
                <el-dropdown-item command="clearCache">清除缓存</el-dropdown-item>
                <el-dropdown-item command="info">文档信息</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- PDF预览 -->
      <div v-if="previewType === 'pdf'" class="pdf-viewer" ref="pdfContainer">
        <canvas ref="pdfCanvas" @wheel="handleWheel"></canvas>
      </div>

      <!-- 图片预览 -->
      <div v-else-if="previewType === 'image'" class="image-viewer">
        <div class="image-container">
          <img 
            :src="previewUrl" 
            :style="imageStyle"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>
      </div>

      <!-- 文本预览 -->
      <div v-else-if="previewType === 'text'" class="text-viewer">
        <div class="text-toolbar">
          <el-button-group>
            <el-button @click="decreaseFontSize" :disabled="fontSize <= 12" size="small">A-</el-button>
            <el-button @click="resetFontSize" size="small">{{ fontSize }}px</el-button>
            <el-button @click="increaseFontSize" :disabled="fontSize >= 24" size="small">A+</el-button>
          </el-button-group>
          <el-switch
            v-model="wordWrap"
            active-text="自动换行"
            inactive-text="不换行"
            size="small"
          />
        </div>
        
        <div class="text-content">
          <pre :style="textStyle" v-html="highlightedText" />
        </div>
      </div>

      <!-- Office文档预览 -->
      <div v-else-if="previewType === 'office'" class="office-viewer">
        <div v-if="conversionProgress" class="conversion-progress">
          <el-progress 
            :percentage="Math.round(conversionProgress.progress * 100)"
            :status="conversionProgress.status === 'failed' ? 'exception' : 'success'"
          />
          <p>{{ conversionProgress.message }}</p>
        </div>
        
        <div v-else-if="convertedPdfUrl" class="converted-pdf-viewer">
          <canvas ref="convertedPdfCanvas" @wheel="handleWheel"></canvas>
        </div>
        
        <div v-else class="office-placeholder">
          <div class="office-icon">
            <el-icon :size="64"><Document /></el-icon>
          </div>
          <h3>{{ document.name }}</h3>
          <p>{{ getOfficeTypeLabel(document.type) }} 文档</p>
          <div class="office-options">
            <p>选择预览方式：</p>
            <el-button type="primary" @click="convertToPdf" :loading="convertLoading">
              转换为PDF预览
            </el-button>
            <el-button @click="downloadDocument">
              下载到本地查看
            </el-button>
          </div>
        </div>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-viewer">
        <el-icon :size="64" color="#909399">
          <Document />
        </el-icon>
        <h3>暂不支持预览此文件类型</h3>
        <p>您可以下载文件后使用相应的应用程序打开</p>
        <el-button type="primary" @click="downloadDocument">
          下载文件
        </el-button>
      </div>
    </div>

    <!-- 文档信息对话框 -->
    <el-dialog v-model="showDocInfo" title="文档信息" width="500px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="文件名">{{ document.name }}</el-descriptions-item>
        <el-descriptions-item label="文件类型">{{ document.type }}</el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(document.size) }}</el-descriptions-item>
        <el-descriptions-item label="上传时间">{{ formatDate(document.create_time) }}</el-descriptions-item>
        <el-descriptions-item label="修改时间">{{ formatDate(document.update_time) }}</el-descriptions-item>
        <el-descriptions-item label="预览状态">
          <el-tag :type="previewStatusType">{{ previewStatusText }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Loading, ArrowLeft, ArrowRight, ZoomIn, ZoomOut, FullScreen,
  Download, More, Document
} from '@element-plus/icons-vue';
import { Session } from '/@/utils/storage';

// PDF.js 导入
import * as pdfjsLib from 'pdfjs-dist';

// 配置PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdfjs-dist/pdf.worker.min.js';

const props = defineProps({
  document: {
    type: Object,
    required: true
  }
});

// 响应式数据
const loading = ref(false);
const loadingMessage = ref('正在加载预览...');
const error = ref('');
const errorDetails = ref('');
const previewUrl = ref('');
const convertedPdfUrl = ref('');
const conversionProgress = ref(null);
const convertLoading = ref(false);
const showDocInfo = ref(false);

// PDF相关
const pdfCanvas = ref(null);
const convertedPdfCanvas = ref(null);
const pdfContainer = ref(null);
const pdf = ref(null);
const convertedPdf = ref(null);
const currentPage = ref(1);
const totalPages = ref(0);
const scale = ref(1.0);

// 图片相关
const rotation = ref(0);

// 文本相关
const textContent = ref('');
const fontSize = ref(14);
const wordWrap = ref(true);

// 计算属性
const previewType = computed(() => {
  const type = props.document.type?.toLowerCase();
  if (!type) return 'unsupported';
  
  if (type.includes('pdf')) return 'pdf';
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) return 'image';
  if (type.includes('text') || ['txt', 'md', 'json', 'xml', 'csv'].includes(type)) return 'text';
  if (type.includes('word') || type.includes('excel') || type.includes('powerpoint') || 
      ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(type)) return 'office';
  
  return 'unsupported';
});

const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotation.value}deg)`,
  transition: 'transform 0.3s ease',
  maxWidth: '100%',
  maxHeight: '100%'
}));

const textStyle = computed(() => ({
  fontSize: `${fontSize.value}px`,
  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
  whiteSpace: wordWrap.value ? 'pre-wrap' : 'pre',
  lineHeight: '1.6',
  padding: '20px',
  margin: 0,
  background: '#fafafa',
  color: '#333'
}));

const highlightedText = computed(() => {
  // 这里可以添加语法高亮逻辑
  return textContent.value;
});

const previewStatusType = computed(() => {
  if (error.value) return 'danger';
  if (loading.value) return 'warning';
  return 'success';
});

const previewStatusText = computed(() => {
  if (error.value) return '预览失败';
  if (loading.value) return '加载中';
  return '预览正常';
});

// 方法
const loadPreview = async () => {
  if (!props.document.id) return;

  loading.value = true;
  error.value = '';
  loadingMessage.value = '正在加载预览...';

  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      error.value = '请先登录';
      console.warn('EnhancedDocumentPreview: No token found, user needs to login');
      return;
    }

    const response = await fetch(`/fastapi/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/preview`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();

    if (result.code === 200) {
      const data = result.data;

      console.log('增强预览数据:', data); // 调试日志

      if (data.requires_conversion) {
        // 需要转换的Office文档
        loadingMessage.value = '检测到Office文档，准备转换...';
      } else if (data.content_type === 'text/plain' && data.content) {
        // 文本内容直接显示
        textContent.value = data.content;
        console.log('显示文本内容:', data.content.substring(0, 100) + '...');
      } else if (data.content_type === 'text/plain' && data.url) {
        // 文本文件但需要通过URL获取内容
        console.log('通过URL获取文本内容:', data.url);
        await loadTextContentFromUrl(data.url);
      } else if (data.url) {
        // 其他类型，使用URL
        previewUrl.value = data.url;
        console.log('设置预览URL:', data.url);

        if (previewType.value === 'pdf') {
          await loadPDF(data.url);
        }
      } else if (data.content_type === 'unknown' && data.url) {
        // 未知类型，尝试作为文本处理
        console.log('未知类型，尝试作为文本处理:', data.url);
        await loadTextContentFromUrl(data.url);
      } else {
        console.warn('无法处理的预览数据:', data);
        ElMessage.warning(data.message || '无法预览此文档');
      }
    } else {
      throw new Error(result.message || '预览失败');
    }
  } catch (err) {
    error.value = '加载预览失败';
    errorDetails.value = err.message;
    console.error('Preview load error:', err);
  } finally {
    loading.value = false;
  }
};

const loadPDF = async (url) => {
  try {
    loadingMessage.value = '正在加载PDF文档...';
    
    pdf.value = await pdfjsLib.getDocument(url).promise;
    totalPages.value = pdf.value.numPages;
    currentPage.value = 1;
    
    await renderPage(1);
  } catch (error) {
    console.error('PDF加载失败:', error);
    throw new Error('PDF文档加载失败');
  }
};

const renderPage = async (pageNum) => {
  if (!pdf.value) return;
  
  try {
    const page = await pdf.value.getPage(pageNum);
    const viewport = page.getViewport({ scale: scale.value });
    
    const canvas = pdfCanvas.value;
    const context = canvas.getContext('2d');
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise;
  } catch (error) {
    console.error('PDF页面渲染失败:', error);
  }
};

// 页面控制
const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(scale.value + 0.25, 3);
  }
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(scale.value - 0.25, 0.5);
  }
};

const resetZoom = () => {
  scale.value = 1.0;
};

// 文本控制
const increaseFontSize = () => {
  if (fontSize.value < 24) {
    fontSize.value += 2;
  }
};

const decreaseFontSize = () => {
  if (fontSize.value > 12) {
    fontSize.value -= 2;
  }
};

const resetFontSize = () => {
  fontSize.value = 14;
};

// Office文档转换
const convertToPdf = async () => {
  convertLoading.value = true;

  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    const response = await fetch(`/fastapi/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/convert-to-pdf`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      if (result.code === 200) {
        const data = result.data;

        if (data.status === 'text_preview') {
          // 文本文件，直接加载预览
          ElMessage.success('文本文件可直接预览');
          await loadPreview(); // 重新加载预览
        } else if (data.status === 'pdf_ready') {
          // PDF文件，直接预览
          ElMessage.success('PDF文件可直接预览');
          await loadPreview(); // 重新加载预览
        } else if (data.status === 'not_supported') {
          // 不支持转换的文件类型
          ElMessage.info(data.message || '此文档类型不支持转换');
        } else if (data.pdf_url) {
          // Office文档转换成功
          convertedPdfUrl.value = data.pdf_url;

          // 如果有任务ID，监控转换进度
          if (data.task_id) {
            monitorConversionProgress(data.task_id);
          }

          ElMessage.success('转换成功');
        } else {
          throw new Error(data.message || '转换失败');
        }
      } else {
        throw new Error(result.message || '转换失败');
      }
    } else {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (err) {
    console.error('PDF转换失败:', err);
    ElMessage.error(`PDF转换失败: ${err instanceof Error ? err.message : '未知错误'}`);
  } finally {
    convertLoading.value = false;
  }
};

const monitorConversionProgress = async (taskId) => {
  const checkProgress = async () => {
    try {
      const response = await fetch(`/api/iot/v1/documents/conversion-progress/${taskId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          conversionProgress.value = result.data;
          
          if (result.data.status === 'completed') {
            conversionProgress.value = null;
            // 重新加载预览
            await loadPreview();
          } else if (result.data.status === 'failed') {
            ElMessage.error('文档转换失败');
            conversionProgress.value = null;
          } else {
            // 继续监控
            setTimeout(checkProgress, 2000);
          }
        }
      }
    } catch (error) {
      console.error('获取转换进度失败:', error);
    }
  };
  
  checkProgress();
};

// 其他功能
const downloadDocument = async () => {
  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    const downloadUrl = `/fastapi/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`;

    // 使用fetch下载文件
    const response = await fetch(downloadUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = props.document.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success('开始下载文件');
    } else {
      throw new Error(`下载失败: ${response.status}`);
    }
  } catch (err) {
    console.error('下载失败:', err);
    ElMessage.error(`下载失败: ${err instanceof Error ? err.message : '未知错误'}`);
  }
};

const loadTextContentFromUrl = async (url) => {
  try {
    console.log('从URL加载文本内容:', url);

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;

    const response = await fetch(fullUrl, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token')}`,
        'Accept': 'text/plain, application/json, */*'
      }
    });

    if (response.ok) {
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        // 如果返回的是JSON，可能是错误响应
        const jsonData = await response.json();
        if (jsonData.code === 200 && jsonData.data?.content) {
          textContent.value = jsonData.data.content;
        } else {
          throw new Error(jsonData.message || '获取文本内容失败');
        }
      } else {
        // 直接作为文本处理
        const text = await response.text();
        textContent.value = text;
        console.log('成功加载文本内容，长度:', text.length);
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (err) {
    console.error('从URL加载文本内容失败:', err);
    error.value = '加载文本内容失败';
    errorDetails.value = err.message;
    ElMessage.error(`加载文本内容失败: ${err.message}`);
  }
};

const retryPreview = () => {
  loadPreview();
};

const toggleFullscreen = () => {
  // 实现全屏功能
  const element = document.querySelector('.enhanced-document-preview');
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    element.requestFullscreen();
  }
};

const handleMenuCommand = async (command) => {
  switch (command) {
    case 'refresh':
      await loadPreview();
      ElMessage.success('预览已刷新');
      break;
    case 'clearCache':
      try {
        // 获取token
        const token = Session.get('token');
        if (!token) {
          ElMessage.error('请先登录');
          return;
        }

        const response = await fetch(`/fastapi/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/cache`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          ElMessage.success('缓存已清除');
          await loadPreview();
        } else {
          throw new Error(`清除缓存失败: ${response.status}`);
        }
      } catch (error) {
        console.error('清除缓存失败:', error);
        ElMessage.error(`清除缓存失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      break;
    case 'info':
      showDocInfo.value = true;
      break;
  }
};

const handleWheel = (event) => {
  if (event.ctrlKey) {
    event.preventDefault();
    if (event.deltaY < 0) {
      zoomIn();
    } else {
      zoomOut();
    }
  }
};

const handleImageLoad = () => {
  console.log('图片加载成功');
};

const handleImageError = () => {
  error.value = '图片加载失败';
};

// 工具函数
const getOfficeTypeLabel = (type) => {
  const typeMap = {
    'docx': 'Word文档',
    'doc': 'Word文档',
    'xlsx': 'Excel表格',
    'xls': 'Excel表格',
    'pptx': 'PowerPoint演示文稿',
    'ppt': 'PowerPoint演示文稿'
  };
  return typeMap[type?.toLowerCase()] || '未知格式';
};

const formatFileSize = (bytes) => {
  if (!bytes) return '未知';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const formatDate = (dateString) => {
  if (!dateString) return '未知';
  return new Date(dateString).toLocaleString();
};

// 监听器
watch(currentPage, (newPage) => {
  if (pdf.value) {
    renderPage(newPage);
  }
});

watch(scale, () => {
  if (pdf.value) {
    renderPage(currentPage.value);
  }
});

// 生命周期
onMounted(() => {
  // 检查是否有token再加载预览
  const token = Session.get('token');
  if (token) {
    loadPreview();
  } else {
    console.log('EnhancedDocumentPreview: No token found, waiting for user login');
  }
});

onUnmounted(() => {
  // 清理资源
  if (pdf.value) {
    pdf.value.destroy();
  }
  if (convertedPdf.value) {
    convertedPdf.value.destroy();
  }
});
</script>

<style scoped>
.enhanced-document-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.loading-container {
  padding: 20px;
  text-align: center;
}

.loading-text {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
}

.error-container {
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  margin: 0 8px;
  color: #666;
  font-size: 14px;
}

/* PDF预览样式 */
.pdf-viewer {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: #e5e5e5;
}

.pdf-viewer canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  cursor: grab;
}

.pdf-viewer canvas:active {
  cursor: grabbing;
}

/* 图片预览样式 */
.image-viewer {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f0f0;
  padding: 20px;
}

.image-container {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文本预览样式 */
.text-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.text-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.text-content {
  flex: 1;
  overflow: auto;
  background: white;
}

.text-content pre {
  border: none;
  border-radius: 0;
  min-height: 100%;
}

/* Office文档预览样式 */
.office-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
  padding: 40px;
}

.conversion-progress {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.conversion-progress p {
  margin-top: 12px;
  color: #666;
}

.converted-pdf-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: auto;
  background: #e5e5e5;
  padding: 20px;
}

.converted-pdf-viewer canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
}

.office-placeholder {
  text-align: center;
  max-width: 400px;
}

.office-icon {
  margin-bottom: 20px;
  color: #909399;
}

.office-placeholder h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.office-placeholder p {
  margin: 0 0 24px 0;
  color: #666;
}

.office-options p {
  margin-bottom: 16px;
  font-weight: 500;
}

.office-options .el-button {
  margin: 0 8px;
}

/* 不支持文件类型样式 */
.unsupported-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
  padding: 40px;
  text-align: center;
}

.unsupported-viewer h3 {
  margin: 20px 0 8px 0;
  color: #303133;
}

.unsupported-viewer p {
  margin: 0 0 24px 0;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;
  }

  .toolbar-left {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pdf-viewer,
  .image-viewer {
    padding: 10px;
  }

  .office-viewer,
  .unsupported-viewer {
    padding: 20px;
  }
}

/* 全屏模式样式 */
.enhanced-document-preview:fullscreen {
  background: #000;
}

.enhanced-document-preview:fullscreen .preview-toolbar {
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.enhanced-document-preview:fullscreen .pdf-viewer,
.enhanced-document-preview:fullscreen .image-viewer {
  background: #000;
}

/* 动画效果 */
.preview-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.pdf-viewer::-webkit-scrollbar,
.image-viewer::-webkit-scrollbar,
.text-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-viewer::-webkit-scrollbar-track,
.image-viewer::-webkit-scrollbar-track,
.text-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb,
.image-viewer::-webkit-scrollbar-thumb,
.text-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb:hover,
.image-viewer::-webkit-scrollbar-thumb:hover,
.text-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
