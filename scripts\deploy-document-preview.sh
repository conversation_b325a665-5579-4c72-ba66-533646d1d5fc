#!/bin/bash

# 文档预览服务部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p docker/nginx/conf.d
    mkdir -p docker/prometheus
    mkdir -p docker/grafana/provisioning
    mkdir -p logs
    mkdir -p data/redis
    mkdir -p data/converter
    
    log_success "目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置脚本执行权限
    chmod +x docker/document-converter/start.sh
    chmod +x scripts/*.sh
    
    # 设置数据目录权限
    sudo chown -R $USER:$USER data/
    
    log_success "权限设置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建文档转换服务镜像
    docker-compose -f docker-compose.document-preview.yml build document-converter
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动文档预览服务..."
    
    # 启动基础服务
    docker-compose -f docker-compose.document-preview.yml up -d redis-cache document-converter nginx-proxy
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services
    
    log_success "文档预览服务启动完成"
}

# 启动监控服务
start_monitoring() {
    log_info "启动监控服务..."
    
    docker-compose -f docker-compose.document-preview.yml --profile monitoring up -d
    
    log_success "监控服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Redis
    if docker-compose -f docker-compose.document-preview.yml exec -T redis-cache redis-cli ping | grep -q PONG; then
        log_success "Redis服务正常"
    else
        log_error "Redis服务异常"
        return 1
    fi
    
    # 检查文档转换服务
    if curl -f http://localhost:8080/health &> /dev/null; then
        log_success "文档转换服务正常"
    else
        log_error "文档转换服务异常"
        return 1
    fi
    
    # 检查Nginx
    if curl -f http://localhost/nginx-health &> /dev/null; then
        log_success "Nginx服务正常"
    else
        log_error "Nginx服务异常"
        return 1
    fi
    
    log_success "所有服务状态正常"
}

# 显示服务信息
show_service_info() {
    log_info "服务信息："
    echo "=================================="
    echo "文档转换服务: http://localhost:8080"
    echo "Nginx代理: http://localhost"
    echo "Redis缓存: localhost:6379"
    echo "=================================="
    echo ""
    echo "监控服务（可选）："
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3000 (admin/admin123)"
    echo "=================================="
}

# 停止服务
stop_services() {
    log_info "停止文档预览服务..."
    
    docker-compose -f docker-compose.document-preview.yml down
    
    log_success "服务已停止"
}

# 清理数据
cleanup_data() {
    log_warning "这将删除所有数据，包括缓存和转换结果"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理数据..."
        
        docker-compose -f docker-compose.document-preview.yml down -v
        docker system prune -f
        
        log_success "数据清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 查看日志
view_logs() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        log_info "查看所有服务日志..."
        docker-compose -f docker-compose.document-preview.yml logs -f
    else
        log_info "查看 $service 服务日志..."
        docker-compose -f docker-compose.document-preview.yml logs -f "$service"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        "start")
            check_dependencies
            create_directories
            set_permissions
            build_images
            start_services
            show_service_info
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 5
            start_services
            ;;
        "status")
            check_services
            ;;
        "logs")
            view_logs "${2:-}"
            ;;
        "monitoring")
            start_monitoring
            ;;
        "cleanup")
            cleanup_data
            ;;
        "build")
            build_images
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs|monitoring|cleanup|build}"
            echo ""
            echo "命令说明："
            echo "  start      - 启动文档预览服务"
            echo "  stop       - 停止服务"
            echo "  restart    - 重启服务"
            echo "  status     - 检查服务状态"
            echo "  logs       - 查看日志 (可指定服务名)"
            echo "  monitoring - 启动监控服务"
            echo "  cleanup    - 清理所有数据"
            echo "  build      - 重新构建镜像"
            echo ""
            echo "示例："
            echo "  $0 start                    # 启动服务"
            echo "  $0 logs document-converter  # 查看转换服务日志"
            echo "  $0 monitoring              # 启动监控"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
