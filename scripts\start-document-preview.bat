@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 文档预览功能启动脚本 (Windows版本)

echo ============================================================
echo 文档预览功能启动
echo ============================================================

:: 检查Python
echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或不在PATH中
    pause
    exit /b 1
)
echo [SUCCESS] Python环境正常

:: 检查Python依赖
echo [INFO] 检查Python依赖...
python -c "import redis, fastapi, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python依赖缺失
    echo [INFO] 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)
echo [SUCCESS] Python依赖检查通过

:: 检查Redis (可选)
echo [INFO] 检查Redis服务...
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Redis服务未运行
    echo [INFO] 部分缓存功能可能不可用
    echo [INFO] 可以使用Docker启动Redis: docker run -d -p 6379:6379 redis:alpine
) else (
    echo [SUCCESS] Redis服务正常
)

:: 检查LibreOffice (可选)
echo [INFO] 检查LibreOffice...
libreoffice --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] LibreOffice未安装
    echo [INFO] Office文档转换功能将不可用
    echo [INFO] 请从官网下载安装: https://www.libreoffice.org/
) else (
    echo [SUCCESS] LibreOffice已安装
)

:: 检查项目结构
echo [INFO] 检查项目结构...
if not exist "backend\main.py" (
    echo [ERROR] 未找到backend\main.py文件
    echo [INFO] 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo [SUCCESS] 项目结构正常

echo.
echo [INFO] 准备启动FastAPI应用...
echo [INFO] 服务将在以下地址可用:
echo [INFO]   主应用: http://localhost:8000
echo [INFO]   API文档: http://localhost:8000/docs
echo [INFO]   文档预览API: http://localhost:8000/api/iot/v1/documents/
echo.
echo [INFO] 按 Ctrl+C 停止服务
echo.

:: 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%CD%

:: 启动FastAPI应用
cd backend
echo [INFO] 启动命令: python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

pause
